[tool.poetry]
name = "pycam"
version = "0.1.0"
description = ""
authors = ["Kei18 <<EMAIL>>"]
readme = "README.md"
packages = [{include = "pycam"}]

[tool.poetry.dependencies]
python = "^3.11"
numpy = "^1.25.1"
jupyterlab = ">=3.0.0,<4.0.0a0"
matplotlib = "^3.7.2"
pytest = "^7.4.0"
loguru = "^0.7.2"


[tool.poetry.group.dev.dependencies]
jupyterlab-lsp = "^4.2.0"
jupyterlab-code-formatter = "^2.2.1"
black = "^23.7.0"
isort = "^5.12.0"
pre-commit = "^3.3.3"
python-lsp-server = "^1.7.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
