<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;8ba22a36-e8b6-440e-b12f-747750238d52&quot;,&quot;conversations&quot;:{&quot;262c2197-69cf-4fa8-9bf2-bde9abf27bf2&quot;:{&quot;id&quot;:&quot;262c2197-69cf-4fa8-9bf2-bde9abf27bf2&quot;,&quot;createdAtIso&quot;:&quot;2025-06-05T07:22:35.974Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-05T07:22:35.974Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;959ebab2-8e74-461c-b5ff-d9397a8ce908&quot;},&quot;8ba22a36-e8b6-440e-b12f-747750238d52&quot;:{&quot;id&quot;:&quot;8ba22a36-e8b6-440e-b12f-747750238d52&quot;,&quot;createdAtIso&quot;:&quot;2025-06-05T07:22:36.079Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-05T07:22:36.079Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;1b59316d-6aed-4751-8070-1c8122054569&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>