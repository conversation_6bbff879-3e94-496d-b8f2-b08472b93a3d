{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d5ca6ebe-6a10-4c03-8784-7ef76935eeb8", "metadata": {"tags": []}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "81e5727c-7c8a-4202-9ca6-70216f958a5f", "metadata": {"tags": []}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from pycam import (\n", "    LaCAM,\n", "    get_grid,\n", "    get_scenario,\n", "    is_valid_mapf_solution,\n", "    save_configs_for_visualizer,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "a0dd5c06-ed82-4cec-9525-95774aade340", "metadata": {}, "outputs": [], "source": ["# define problems\n", "\n", "map_file = Path(\"../assets/tunnel.map\")\n", "scen_file = Path(\"../assets/tunnel.scen\")\n", "N = 4\n", "\n", "grid = get_grid(map_file)\n", "starts, goals = get_scenario(scen_file, N)"]}, {"cell_type": "code", "execution_count": 4, "id": "36804ce7-8b0c-4333-8b1b-8b681a98be3f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2023-12-02 14:28:28\u001b[0m \u001b[34m\u001b[1m   0ms  start solving MAPF\u001b[0m\n", "\u001b[32m2023-12-02 14:28:29\u001b[0m \u001b[34m\u001b[1m1606ms  initial solution found, cost=72\u001b[0m\n", "\u001b[32m2023-12-02 14:28:31\u001b[0m \u001b[34m\u001b[1m3000ms  suboptimal solution, cost=57\u001b[0m\n", "CPU times: user 3.44 s, sys: 589 ms, total: 4.03 s\n", "Wall time: 3 s\n"]}], "source": ["%%time\n", "\n", "# solve MAPF\n", "planner = LaCAM()\n", "solution = planner.solve(grid, starts, goals)"]}, {"cell_type": "code", "execution_count": 5, "id": "6c28d4eb-4ef1-4561-bed1-86294eb66c41", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["validation: True\n"]}], "source": ["# validation: True -> feasible\n", "print(f\"validation: {is_valid_mapf_solution(grid, starts, goals, solution)}\")\n", "\n", "# save result\n", "save_configs_for_visualizer(solution, \"./local/tmp.txt\")"]}, {"cell_type": "code", "execution_count": 6, "id": "24208ea0-21ea-4a02-83fc-3d1b3e428e45", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["keys for visualizer\n", "- p : play or pause\n", "- l : loop or not\n", "- r : reset\n", "- v : show virtual line to goals\n", "- f : show agent & node id\n", "- g : show goals\n", "- right : progress\n", "- left  : back\n", "- up    : speed up\n", "- down  : speed down\n", "- space : screenshot (saved in Desktop)\n", "- esc : terminate\n"]}], "source": ["# visualize, do not forget to install https://github.com/Kei18/mapf-visualizer\n", "!mapf-visualizer ../assets/tunnel.map ./local/tmp.txt"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}