<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="172a0247-cc27-462f-a3b3-d6a648b9f236" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2y4KYmmHT5VRndyx7QDinMpBgYf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DSTableLoadingError": "加载表数据失败",
    "Notification.DoNotAsk-DSTableLoadingError": "true",
    "Python.app.executor": "Debug",
    "Python.lacam.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/crscu/route_palnning/codes/py-lacam-main",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="172a0247-cc27-462f-a3b3-d6a648b9f236" name="更改" comment="" />
      <created>1749088456210</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749088456210</updated>
      <workItem from="1749088457381" duration="1914000" />
      <workItem from="1749094965643" duration="507000" />
      <workItem from="1749095487326" duration="3010000" />
      <workItem from="1749105450746" duration="2671000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/pycam/lacam.py</url>
          <line>112</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/py_lacam_main$lacam.coverage" NAME="lacam 覆盖结果" MODIFIED="1749107208327" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pycam" />
    <SUITE FILE_PATH="coverage/py_lacam_main$app.coverage" NAME="app 覆盖结果" MODIFIED="1749107309117" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>